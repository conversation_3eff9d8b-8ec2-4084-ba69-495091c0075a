# Performance Optimization Migration Guide

## Quick Implementation Steps

### Step 1: Add New Dependencies

Add these to your `pubspec.yaml` if not already present:
```yaml
dependencies:
  google_mobile_ads: ^latest_version
  flutter: 
    sdk: flutter
```

### Step 2: Initialize Services

In your `main.dart`, ensure the PremiumAdManager is initialized:
```dart
// In main() function, after other initializations:
Get.put(PremiumAdManager(), permanent: true);
```

### Step 3: Update Imports

The updated `OptimizedMergedItemsList` requires these imports:
```dart
import 'package:bibl/utils/performance_optimizer.dart';
import 'package:bibl/services/premium_ad_manager.dart';
```

### Step 4: File Structure

Ensure these new files exist in your project:
```
lib/
├── utils/
│   └── performance_optimizer.dart       (NEW)
├── services/
│   └── premium_ad_manager.dart         (NEW)
└── widgets/
    └── optimized_merged_items_list.dart (UPDATED)
```

### Step 5: Test the Changes

1. **Run the app** and navigate to screens with lists
2. **Scroll rapidly** up and down - should be smooth
3. **Check ad loading** - should appear without layout shifts
4. **Monitor performance** in Flutter DevTools

### Step 6: Verify Ad Configuration

Ensure your ad unit IDs are correct in `PremiumAdManager`:
```dart
// Android ad units
'home_banner_1': 'ca-app-pub-YOUR_ID_HERE',
'home_banner_2': 'ca-app-pub-YOUR_ID_HERE',

// iOS ad units  
'home_banner_1': 'ca-app-pub-YOUR_ID_HERE',
'home_banner_2': 'ca-app-pub-YOUR_ID_HERE',
```

### Step 7: Clean and Rebuild

```bash
flutter clean
flutter pub get
flutter run
```

## Troubleshooting

### If ads don't load:
1. Check internet connection
2. Verify ad unit IDs are correct
3. Check if test ads work with test IDs
4. Look for errors in debug console

### If scrolling is still laggy:
1. Profile with Flutter DevTools
2. Check if images are being preloaded
3. Verify RepaintBoundary widgets are in place
4. Reduce complex animations during scroll

### If memory usage is high:
1. Check cache sizes in PremiumAdManager
2. Verify image cache is being cleared
3. Use memory profiler to identify leaks

## Performance Checklist

- [ ] No duplicate ad loads in console
- [ ] Smooth 60 FPS scrolling
- [ ] No content shifting when ads load
- [ ] Fast initial page load
- [ ] Memory usage stays stable
- [ ] No "jank" warnings in console

## Need Help?

Check the detailed documentation in `PERFORMANCE_OPTIMIZATION_README.md` for more information about the implementation.
